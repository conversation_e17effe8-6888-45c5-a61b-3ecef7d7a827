// AboutSection.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { User, Award, Calendar, MapPin } from 'lucide-react';

const AboutSection: React.FC = () => {
  const skills = [
    'JavaScript/TypeScript', 'React', 'Node.js', 'Python', 
    'AWS', 'Docker', 'MongoDB', 'PostgreSQL'
  ];

  const experiences = [
    {
      role: 'Senior Frontend Developer',
      company: 'Tech Innovations Inc.',
      period: '2020 - Present',
      description: 'Leading frontend development for enterprise SaaS products'
    },
    {
      role: 'Full Stack Developer',
      company: 'Digital Solutions Ltd.',
      period: '2018 - 2020',
      description: 'Developed and maintained multiple client web applications'
    },
    {
      role: 'Junior Developer',
      company: 'WebCraft Studios',
      period: '2016 - 2018',
      description: 'Built responsive websites and web applications'
    }
  ];

  return (
    <section id="about" className="min-h-screen bg-gray-900 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">About Me</h2>
          <div className="w-20 h-1 bg-gray-600 mx-auto"></div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <div className="bg-gray-800 rounded-2xl p-8 border border-gray-700">
              <div className="flex items-center mb-6">
                <User className="text-gray-400 mr-3" size={24} />
                <h3 className="text-2xl font-bold text-white">Who I Am</h3>
              </div>
              
              <p className="text-gray-300 mb-6 leading-relaxed">
                I'm a passionate full-stack developer with over 6 years of experience creating 
                digital solutions that make a difference. My journey in tech started with a 
                Computer Science degree and has evolved through various challenging projects 
                and roles.
              </p>
              
              <p className="text-gray-300 mb-8 leading-relaxed">
                I specialize in building scalable web applications with modern technologies 
                and have a keen eye for clean, efficient code. When I'm not coding, you can 
                find me contributing to open-source projects or exploring new tech trends.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <MapPin className="text-gray-500 mr-2" size={18} />
                  <span className="text-gray-400">San Francisco, CA</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="text-gray-500 mr-2" size={18} />
                  <span className="text-gray-400">Available for work</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="space-y-8"
          >
            <div className="bg-gray-800 rounded-2xl p-8 border border-gray-700">
              <div className="flex items-center mb-6">
                <Award className="text-gray-400 mr-3" size={24} />
                <h3 className="text-2xl font-bold text-white">Experience</h3>
              </div>
              
              <div className="space-y-6">
                {experiences.map((exp, index) => (
                  <motion.div 
                    key={index}
                    className="relative pl-8 border-l-2 border-gray-700"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="absolute -left-2.5 top-0 w-5 h-5 rounded-full bg-gray-600"></div>
                    <span className="text-gray-400 text-sm">{exp.period}</span>
                    <h4 className="text-xl font-semibold text-white mt-1">{exp.role}</h4>
                    <p className="text-gray-500 font-medium">{exp.company}</p>
                    <p className="text-gray-300 mt-2">{exp.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 rounded-2xl p-8 border border-gray-700">
              <h3 className="text-2xl font-bold text-white mb-6">Technical Skills</h3>
              <div className="flex flex-wrap gap-3">
                {skills.map((skill, index) => (
                  <motion.span
                    key={index}
                    className="px-4 py-2 bg-gray-700 text-gray-300 rounded-full text-sm font-medium"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    whileHover={{ y: -3, backgroundColor: '#374151' }}
                  >
                    {skill}
                  </motion.span>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;