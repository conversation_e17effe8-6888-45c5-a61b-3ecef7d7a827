// ProjectsSection.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Github, Star, GitFork } from 'lucide-react';

interface Project {
  id: number;
  title: string;
  description: string;
  technologies: string[];
  image: string;
  githubUrl: string;
  liveUrl: string;
  stars: number;
  forks: number;
}

const ProjectsSection: React.FC = () => {
  const projects: Project[] = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'Full-featured online shopping platform with payment integration and inventory management.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=E-Commerce+Platform',
      githubUrl: '#',
      liveUrl: '#',
      stars: 42,
      forks: 18
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'Collaborative task management solution with real-time updates and team features.',
      technologies: ['TypeScript', 'Firebase', 'Tailwind CSS'],
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=Task+Manager',
      githubUrl: '#',
      liveUrl: '#',
      stars: 28,
      forks: 9
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'Real-time weather application with forecasting and location-based services.',
      technologies: ['React', 'OpenWeather API', 'Chart.js'],
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=Weather+App',
      githubUrl: '#',
      liveUrl: '#',
      stars: 15,
      forks: 5
    },
    {
      id: 4,
      title: 'Portfolio Generator',
      description: 'Automated portfolio builder for developers with customizable templates.',
      technologies: ['Next.js', 'GraphQL', 'Tailwind CSS'],
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=Portfolio+Generator',
      githubUrl: '#',
      liveUrl: '#',
      stars: 36,
      forks: 12
    }
  ];

  return (
    <section id="projects" className="min-h-screen bg-black py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Featured Projects</h2>
          <div className="w-20 h-1 bg-gray-600 mx-auto"></div>
          <p className="text-gray-400 mt-6 max-w-2xl mx-auto">
            Here are some of my recent projects. Each project reflects my passion for clean code and innovative solutions.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              className="bg-gray-900 rounded-2xl overflow-hidden border border-gray-800 hover:border-gray-700 transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
            >
              <div className="relative">
                <img 
                  src={project.image} 
                  alt={project.title} 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                <div className="absolute bottom-4 left-4 flex gap-2">
                  <div className="flex items-center bg-black/70 px-2 py-1 rounded text-gray-300 text-sm">
                    <Star size={14} className="mr-1" />
                    {project.stars}
                  </div>
                  <div className="flex items-center bg-black/70 px-2 py-1 rounded text-gray-300 text-sm">
                    <GitFork size={14} className="mr-1" />
                    {project.forks}
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-2">{project.title}</h3>
                <p className="text-gray-400 mb-4">{project.description}</p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.map((tech, techIndex) => (
                    <span 
                      key={techIndex} 
                      className="px-3 py-1 bg-gray-800 text-gray-300 text-xs rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="flex gap-4">
                  <a 
                    href={project.githubUrl} 
                    className="flex items-center text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    <Github size={18} className="mr-2" />
                    Code
                  </a>
                  <a 
                    href={project.liveUrl} 
                    className="flex items-center text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    <ExternalLink size={18} className="mr-2" />
                    Live Demo
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <button className="border-2 border-gray-700 text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-800 transition-colors duration-300">
            View All Projects
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default ProjectsSection;