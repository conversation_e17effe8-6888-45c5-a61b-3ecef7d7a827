// BlogSection.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, ArrowRight } from 'lucide-react';

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  readTime: string;
  category: string;
  image: string;
}

const BlogSection: React.FC = () => {
  const blogPosts: BlogPost[] = [
    {
      id: 1,
      title: 'Building Scalable React Applications',
      excerpt: 'Learn how to structure large React applications for maintainability and performance.',
      date: 'May 15, 2023',
      readTime: '8 min read',
      category: 'React',
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=React+Architecture'
    },
    {
      id: 2,
      title: 'State Management in Modern Web Apps',
      excerpt: 'Comparing different state management solutions and when to use each one.',
      date: 'Apr 28, 2023',
      readTime: '10 min read',
      category: 'JavaScript',
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=State+Management'
    },
    {
      id: 3,
      title: 'Optimizing Web Performance',
      excerpt: 'Practical techniques to improve loading times and user experience.',
      date: 'Apr 12, 2023',
      readTime: '12 min read',
      category: 'Performance',
      image: 'https://placehold.co/600x400/1f2937/9ca3af?text=Web+Performance'
    }
  ];

  return (
    <section id="blog" className="min-h-screen bg-gray-900 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Latest Articles</h2>
          <div className="w-20 h-1 bg-gray-600 mx-auto"></div>
          <p className="text-gray-400 mt-6 max-w-2xl mx-auto">
            Thoughts, tutorials, and insights on web development and technology.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <motion.article
              key={post.id}
              className="bg-gray-800 rounded-2xl overflow-hidden border border-gray-700 hover:border-gray-600 transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
            >
              <img 
                src={post.image} 
                alt={post.title} 
                className="w-full h-48 object-cover"
              />
              
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="px-3 py-1 bg-gray-700 text-gray-300 text-xs rounded-full">
                    {post.category}
                  </span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Clock size={14} className="mr-1" />
                    {post.readTime}
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-3">{post.title}</h3>
                <p className="text-gray-400 mb-4">{post.excerpt}</p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-500 text-sm">
                    <Calendar size={14} className="mr-1" />
                    {post.date}
                  </div>
                  <button className="flex items-center text-gray-400 hover:text-white transition-colors duration-300">
                    Read More <ArrowRight size={16} className="ml-1" />
                  </button>
                </div>
              </div>
            </motion.article>
          ))}
        </div>

        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <button className="border-2 border-gray-700 text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-800 transition-colors duration-300">
            View All Articles
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default BlogSection;