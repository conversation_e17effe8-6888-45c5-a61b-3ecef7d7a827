// HeroSection.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { Github, Linkedin, Mail, ExternalLink, Code, Smartphone, Zap } from 'lucide-react';

const HeroSection: React.FC = () => {
  return (
    <section id="home" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black text-white pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.h1 
              className="text-4xl md:text-6xl font-bold mb-6 leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.8 }}
            >
              Hi, I'm <span className="text-gray-300">Alex</span>
              <br />
              <span className="bg-gradient-to-r from-gray-300 to-gray-500 bg-clip-text text-transparent">
                Full Stack Developer
              </span>
            </motion.h1>
            
            <motion.p 
              className="text-xl text-gray-400 mb-8 max-w-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              I craft exceptional digital experiences with clean code and innovative solutions. 
              Specializing in modern web technologies and user-centered design.
            </motion.p>
            
            <motion.div 
              className="flex flex-wrap gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <motion.button 
                className="bg-white text-black px-8 py-3 rounded-full font-semibold hover:bg-gray-200 transition-colors duration-300 flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View Projects <ExternalLink size={18} />
              </motion.button>
              <motion.button 
                className="border-2 border-gray-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-800 transition-colors duration-300 flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Download CV <Code size={18} />
              </motion.button>
            </motion.div>
            
            <motion.div 
              className="flex gap-6 mt-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              {[Github, Linkedin, Mail].map((Icon, index) => (
                <motion.a
                  key={index}
                  href="#"
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                  whileHover={{ y: -5, scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label={`Social link ${index + 1}`}
                >
                  <Icon size={24} />
                </motion.a>
              ))}
            </motion.div>
          </motion.div>
          
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="relative w-full max-w-lg mx-auto">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-900 rounded-full blur-3xl opacity-30"
                animate={{
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
              <motion.div 
                className="relative bg-gray-800 border border-gray-700 rounded-2xl p-8 backdrop-blur-sm"
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-gray-900 p-6 rounded-xl border border-gray-700">
                    <Code className="text-gray-300 mb-4" size={32} />
                    <h3 className="text-white font-semibold mb-2">Web Development</h3>
                    <p className="text-gray-400 text-sm">Modern frameworks & clean code</p>
                  </div>
                  <div className="bg-gray-900 p-6 rounded-xl border border-gray-700">
                    <Smartphone className="text-gray-300 mb-4" size={32} />
                    <h3 className="text-white font-semibold mb-2">Responsive Design</h3>
                    <p className="text-gray-400 text-sm">Mobile-first approach</p>
                  </div>
                  <div className="bg-gray-900 p-6 rounded-xl border border-gray-700">
                    <Zap className="text-gray-300 mb-4" size={32} />
                    <h3 className="text-white font-semibold mb-2">Performance</h3>
                    <p className="text-gray-400 text-sm">Optimized for speed</p>
                  </div>
                  <div className="bg-gray-900 p-6 rounded-xl border border-gray-700">
                    <Code className="text-gray-300 mb-4" size={32} />
                    <h3 className="text-white font-semibold mb-2">Scalable</h3>
                    <p className="text-gray-400 text-sm">Built for growth</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;