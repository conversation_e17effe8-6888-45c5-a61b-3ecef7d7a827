// ProjectDetails.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, ExternalLink, Github, Calendar, Star, GitFork } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Project {
  id: number;
  title: string;
  description: string;
  longDescription: string;
  technologies: string[];
  image: string;
  githubUrl: string;
  liveUrl: string;
  date: string;
  stars: number;
  forks: number;
  features: string[];
  challenges: string[];
  solutions: string[];
}

const ProjectDetails: React.FC = () => {
  const navigate = useNavigate();
  
  // Mock project data
  const project: Project = {
    id: 1,
    title: 'E-Commerce Platform',
    description: 'Full-featured online shopping platform with payment integration and inventory management.',
    longDescription: 'This comprehensive e-commerce solution was built to provide businesses with a scalable and secure platform for selling products online. The platform includes features such as product management, user authentication, shopping cart functionality, payment processing, and order management.',
    technologies: ['React', 'Node.js', 'Express', 'MongoDB', 'Stripe', 'Redux', 'Tailwind CSS'],
    image: 'https://placehold.co/800x500/1f2937/9ca3af?text=E-Commerce+Platform',
    githubUrl: '#',
    liveUrl: '#',
    date: 'March 2023',
    stars: 42,
    forks: 18,
    features: [
      'User authentication and profile management',
      'Product catalog with search and filtering',
      'Shopping cart and checkout process',
      'Secure payment integration with Stripe',
      'Order tracking and management',
      'Admin dashboard for inventory management'
    ],
    challenges: [
      'Implementing secure payment processing',
      'Optimizing database queries for performance',
      'Ensuring responsive design across devices',
      'Managing complex state in large application'
    ],
    solutions: [
      'Used Stripe Elements for secure payment forms',
      'Implemented database indexing and aggregation pipelines',
      'Applied mobile-first responsive design principles',
      'Utilized Redux Toolkit for efficient state management'
    ]
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.button
          onClick={() => navigate(-1)}
          className="flex items-center text-gray-400 hover:text-white mb-8 transition-colors duration-300"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <ArrowLeft size={20} className="mr-2" />
          Back to Projects
        </motion.button>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <div className="lg:col-span-2">
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-4xl md:text-5xl font-bold">{project.title}</h1>
                <div className="flex gap-2">
                  <div className="flex items-center bg-gray-800 px-3 py-1 rounded text-gray-300 text-sm">
                    <Star size={14} className="mr-1" />
                    {project.stars}
                  </div>
                  <div className="flex items-center bg-gray-800 px-3 py-1 rounded text-gray-300 text-sm">
                    <GitFork size={14} className="mr-1" />
                    {project.forks}
                  </div>
                </div>
              </div>

              <motion.img
                src={project.image}
                alt={project.title}
                className="w-full h-64 md:h-96 object-cover rounded-2xl mb-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              />

              <div className="mb-12">
                <h2 className="text-2xl font-bold mb-4">Overview</h2>
                <p className="text-gray-300 leading-relaxed mb-6">
                  {project.longDescription}
                </p>
                
                <div className="flex flex-wrap gap-3 mb-8">
                  {project.technologies.map((tech, index) => (
                    <span 
                      key={index} 
                      className="px-4 py-2 bg-gray-800 text-gray-300 rounded-full text-sm"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div>
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <div className="w-3 h-3 bg-gray-600 rounded-full mr-3"></div>
                    Key Features
                  </h3>
                  <ul className="space-y-3">
                    {project.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-gray-500 rounded-full mt-2 mr-3"></div>
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <div className="w-3 h-3 bg-gray-600 rounded-full mr-3"></div>
                    Technical Challenges
                  </h3>
                  <ul className="space-y-3">
                    {project.challenges.map((challenge, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-gray-500 rounded-full mt-2 mr-3"></div>
                        <span className="text-gray-300">{challenge}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="mb-12">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <div className="w-3 h-3 bg-gray-600 rounded-full mr-3"></div>
                  Solutions Implemented
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {project.solutions.map((solution, index) => (
                    <div key={index} className="bg-gray-900 p-4 rounded-lg border border-gray-800">
                      <p className="text-gray-300">{solution}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <div className="bg-gray-900 rounded-2xl p-6 border border-gray-800 sticky top-24">
                <h3 className="text-xl font-bold mb-6">Project Details</h3>
                
                <div className="space-y-6 mb-8">
                  <div>
                    <h4 className="text-gray-500 text-sm uppercase tracking-wide mb-2">Date</h4>
                    <div className="flex items-center text-gray-300">
                      <Calendar size={18} className="mr-2" />
                      {project.date}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-gray-500 text-sm uppercase tracking-wide mb-2">Links</h4>
                    <div className="space-y-3">
                      <a 
                        href={project.liveUrl} 
                        className="flex items-center justify-between w-full bg-gray-800 hover:bg-gray-700 p-3 rounded-lg transition-colors duration-300"
                      >
                        <span>Live Demo</span>
                        <ExternalLink size={18} />
                      </a>
                      <a 
                        href={project.githubUrl} 
                        className="flex items-center justify-between w-full bg-gray-800 hover:bg-gray-700 p-3 rounded-lg transition-colors duration-300"
                      >
                        <span>Source Code</span>
                        <Github size={18} />
                      </a>
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-800">
                  <h4 className="text-gray-500 text-sm uppercase tracking-wide mb-4">Technologies Used</h4>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, index) => (
                      <span 
                        key={index} 
                        className="px-3 py-1 bg-gray-800 text-gray-300 text-xs rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ProjectDetails;