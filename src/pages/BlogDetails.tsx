// BlogDetails.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, Tag, ArrowLeft, User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface BlogPost {
  id: number;
  title: string;
  author: string;
  date: string;
  readTime: string;
  category: string;
  content: string;
  image: string;
  tags: string[];
}

const BlogDetails: React.FC = () => {
  const navigate = useNavigate();
  
  // Mock blog post data
  const blogPost: BlogPost = {
    id: 1,
    title: 'Building Scalable React Applications',
    author: 'Alex Developer',
    date: 'May 15, 2023',
    readTime: '8 min read',
    category: 'React',
    image: 'https://placehold.co/800x400/1f2937/9ca3af?text=React+Architecture',
    tags: ['React', 'JavaScript', 'Frontend', 'Architecture'],
    content: `
      <p>In today's web development landscape, building scalable applications is crucial for long-term success. React, with its component-based architecture, provides an excellent foundation for creating maintainable and performant applications.</p>
      
      <h2 className="text-2xl font-bold mt-8 mb-4">Component Structure</h2>
      <p>One of the key aspects of scalable React applications is proper component organization. I recommend using a feature-based folder structure where each feature contains its components, styles, and tests in the same directory.</p>
      
      <h2 className="text-2xl font-bold mt-8 mb-4">State Management</h2>
      <p>As applications grow, managing state becomes increasingly complex. For medium to large applications, consider using state management libraries like Redux Toolkit or Zustand. For simpler applications, React Context API might suffice.</p>
      
      <h2 className="text-2xl font-bold mt-8 mb-4">Performance Optimization</h2>
      <p>Performance is critical for user experience. Techniques like code splitting, lazy loading components, and memoization can significantly improve your application's performance. Use React.memo for components that render frequently with the same props.</p>
      
      <h2 className="text-2xl font-bold mt-8 mb-4">Testing Strategy</h2>
      <p>A robust testing strategy is essential for scalable applications. Implement unit tests for individual components and integration tests for feature flows. Tools like Jest and React Testing Library make testing React components straightforward.</p>
      
      <p>By following these practices, you'll be well on your way to building React applications that can grow and adapt to changing requirements while maintaining code quality and performance.</p>
    `
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.button
          onClick={() => navigate(-1)}
          className="flex items-center text-gray-400 hover:text-white mb-8 transition-colors duration-300"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <ArrowLeft size={20} className="mr-2" />
          Back to Blog
        </motion.button>

        <motion.article
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-between mb-6">
            <span className="px-3 py-1 bg-gray-800 text-gray-300 text-sm rounded-full">
              {blogPost.category}
            </span>
            <div className="flex items-center text-gray-500 text-sm">
              <Clock size={16} className="mr-1" />
              {blogPost.readTime}
            </div>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold mb-6">{blogPost.title}</h1>

          <div className="flex flex-wrap items-center gap-6 mb-8 text-gray-400">
            <div className="flex items-center">
              <User size={18} className="mr-2" />
              {blogPost.author}
            </div>
            <div className="flex items-center">
              <Calendar size={18} className="mr-2" />
              {blogPost.date}
            </div>
          </div>

          <motion.img
            src={blogPost.image}
            alt={blogPost.title}
            className="w-full h-64 md:h-96 object-cover rounded-2xl mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          />

          <div 
            className="prose prose-invert max-w-none mb-12"
            dangerouslySetInnerHTML={{ __html: blogPost.content }}
          />

          <div className="flex flex-wrap gap-2 mb-12">
            {blogPost.tags.map((tag, index) => (
              <span 
                key={index} 
                className="px-3 py-1 bg-gray-800 text-gray-300 text-sm rounded-full flex items-center"
              >
                <Tag size={14} className="mr-1" />
                {tag}
              </span>
            ))}
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center mr-4">
                <User size={24} />
              </div>
              <div>
                <h4 className="font-semibold">{blogPost.author}</h4>
                <p className="text-gray-400 text-sm">Full Stack Developer & Tech Enthusiast</p>
              </div>
            </div>
          </div>
        </motion.article>
      </div>
    </div>
  );
};

export default BlogDetails;