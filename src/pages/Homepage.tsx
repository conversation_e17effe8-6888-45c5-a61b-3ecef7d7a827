import React from 'react'
import Navbar from '../components/global/Navbar'
import ContactSection from '../components/pages/Contact'
import BlogSection from '../components/pages/Blog'
import ProjectsSection from '../components/pages/Project'
import AboutSection from '../components/pages/About'
import HeroSection from '../components/pages/Hero'

const Homepage: React.FC = () => {
  return (
    <>
    <Navbar setActiveSection={() => {}}  activeSection='home'/>
    <HeroSection />
    <AboutSection />
    <ProjectsSection />
    <BlogSection />
    <ContactSection />
    
    </>
  )
}

export default Homepage